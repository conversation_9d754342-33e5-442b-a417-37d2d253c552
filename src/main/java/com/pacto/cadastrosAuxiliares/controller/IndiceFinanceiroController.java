package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.IndiceFinanceiroDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroIndiceFinanceiroJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.IndiceFinanceiroService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.indicefinanceiro.ExemploRespostaIndiceFinanceiro;
import com.pacto.cadastrosAuxiliares.swagger.respostas.indicefinanceiro.ExemploRespostaListIndiceFinanceiroPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/indice-financeiro")
public class IndiceFinanceiroController {

    private final IndiceFinanceiroService service;

    public IndiceFinanceiroController(IndiceFinanceiroService service) {
        this.service = service;
    }

    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     PaginadorDTO paginadorDTO) {
        try {
            FiltroIndiceFinanceiroJSON filtroIndiceFinanceiroJSON = new FiltroIndiceFinanceiroJSON(filtros);
            return ResponseEntityFactory.ok(service.findAll(filtroIndiceFinanceiroJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> indice(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(service.indice(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluir(@RequestBody IndiceFinanceiroDTO dto) {
        try {
            return ResponseEntityFactory.ok(service.saveOrUpdate(dto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable Integer id ) {
        try {
            service.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
