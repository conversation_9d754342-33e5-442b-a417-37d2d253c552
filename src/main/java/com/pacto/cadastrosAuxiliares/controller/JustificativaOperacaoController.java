package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.JustificativaOperacaoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroJustificativaOperacaoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.JustificativaOperacaoService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.justificativaoperacao.ExemploRespostaJustificativaOperacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.justificativaoperacao.ExemploRespostaListJustificativaOperacaoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/justificativa-operacao")

public class JustificativaOperacaoController {

    private final JustificativaOperacaoService justificativaOperacaoService;

    public JustificativaOperacaoController(JustificativaOperacaoService justificativaOperacaoService) {
        this.justificativaOperacaoService = justificativaOperacaoService;
    }

    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody JustificativaOperacaoDTO justificativaOperacaoDTO){
        try{
            return  ResponseEntityFactory.ok(justificativaOperacaoService.saveOrUpdate(justificativaOperacaoDTO));
        } catch (ServiceException e){
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e){
            StringBuilder result = new StringBuilder(e.toString()+"/n");
            StackTraceElement[] trace = e.getStackTrace();
            for(StackTraceElement stackTraceElement : trace){
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros,
        PaginadorDTO paginadorDTO) {
        try {
            FiltroJustificativaOperacaoJSON filtrosJson = new FiltroJustificativaOperacaoJSON(filtros);
            return ResponseEntityFactory.ok(justificativaOperacaoService.findAll(filtrosJson, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(justificativaOperacaoService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id ) {
        try {
            justificativaOperacaoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}

